#!/bin/bash

# Test setup script for BitNet model functionality
set -e

echo "=== CPU Poem BitNet Model Test Setup ==="
echo ""

# Create necessary directories
echo "Creating directories..."
mkdir -p ./model_cache
mkdir -p ./models

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "Please edit .env file with your actual Qlurk API credentials."
    echo ""
fi

echo "Directories created successfully."
echo ""

# Build the project
echo "Building project..."
cargo build --release

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi

echo ""
echo "=== Running Model Test ==="
echo "This will download and test the BitNet model (may take several minutes)..."
echo ""

# Run the test
cargo run --bin test_model

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Model test completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Ensure your .env file has correct Qlurk API credentials"
    echo "2. Run the main application: cargo run"
    echo ""
else
    echo ""
    echo "❌ Model test failed. Please check the error messages above."
    exit 1
fi