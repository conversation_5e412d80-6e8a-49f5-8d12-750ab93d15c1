#!/bin/bash

# Setup script for BitNet model
set -e

echo "Setting up BitNet model for cpu-poem..."

# Create model directories
mkdir -p ./models
mkdir -p ./model_cache

echo "Model directories created."
echo ""
echo "The application will automatically download the BitNet model on first run."
echo "This may take some time depending on your internet connection."
echo ""
echo "To run the application:"
echo "1. Make sure your .env file is configured with Qlurk credentials"
echo "2. Run: cargo run"
echo ""
echo "The model will be cached in ./model_cache for faster subsequent runs."