# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Rust-based social media bot that automatically responds to Plurk posts with Chinese poetry. The bot monitors specific types of posts (feels, writes, wants, needs) and generates poetic responses using Microsoft's BitNet model loaded directly in the process.

## Architecture

### Core Components

- **Main Application** (`src/main.rs`): Rust application containing all functionality
- **QlurkClient**: Handles Plurk API interactions for polling posts and submitting responses
- **BitNetModel**: Local inference using Microsoft BitNet b1.58-2B-4T model with Candle framework
- **Response Generation**: Direct model inference for Chinese poetry generation

### Key Functions

- `BitNetModel::load()`: Downloads and initializes the BitNet model from HuggingFace
- `BitNetModel::generate_response()`: Performs local inference with Chinese poetry system prompt
- `br2nl()`: Converts HTML breaks to newlines for response formatting
- `run_cycle()`: Main polling and response generation loop

### Execution Flow

1. Loads BitNet model from HuggingFace Hub on startup (cached locally)
2. Continuously polls Plurk API for new posts since last timestamp
3. Filters posts by qualifier types (feels, writes, wants, needs)
4. Generates Chinese poetry responses using local BitNet model inference
5. Posts generated responses back to Plurk
6. Updates timestamp and sleeps 5 seconds before next cycle

## Dependencies

- **candle-core/candle-nn/candle-transformers**: ML framework for model inference
- **hf-hub**: HuggingFace Hub API for model downloading
- **tokenizers**: Text tokenization for model input/output
- **reqwest**: HTTP client for Plurk API calls
- **tokio**: Async runtime

## Development Notes

- **Model Loading**: BitNet model downloaded from HuggingFace on first run, cached locally
- **Memory Management**: Uses CPU inference with efficient 1.58-bit quantized weights
- **Timezone**: Uses UTC timezone for consistent timestamp handling
- **Error Handling**: Comprehensive error handling throughout async operations
- **Text Generation**: Temperature 0.8, top-p 0.9 sampling for creative poetry responses
- **Rate Limiting**: 5-second sleep between polling cycles

## Commands

```bash
# Test model functionality (recommended first step)
./test_setup.sh

# Manual setup
./setup_model.sh

# Test model only
cargo run --bin test_model

# Build and run main application
cargo build --release
cargo run

# Development
cargo check
cargo clippy
```

## Security Considerations

API credentials are stored in `.env` file. Ensure `.env` is added to `.gitignore` and credentials are properly secured. Use `.env.example` as a template for configuration.