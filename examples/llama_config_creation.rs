/// Example demonstrating different ways to create LlamaConfig in candle-transformers 0.8
/// without relying on serde deserialization

use anyhow::Result;
use candle_transformers::models::llama::{Config as LlamaConfig, LlamaEosToks};
use cpu_poem::create_llama_config_for_model_size;
use serde_json;

fn main() -> Result<()> {
    println!("=== LlamaConfig Creation Examples ===\n");

    // Method 1: Using built-in predefined configurations
    println!("1. Using built-in predefined configurations:");
    let config_7b_v1 = LlamaConfig::config_7b_v1(false);
    println!("   7B v1 model: hidden_size={}, num_layers={}", 
             config_7b_v1.hidden_size, config_7b_v1.num_hidden_layers);
    
    let config_7b_v2 = LlamaConfig::config_7b_v2(false);
    println!("   7B v2 model: hidden_size={}, num_layers={}", 
             config_7b_v2.hidden_size, config_7b_v2.num_hidden_layers);

    // Method 2: Using helper function for common model sizes
    println!("\n2. Using helper function for common model sizes:");
    let config_2b = create_llama_config_for_model_size("2b", false)?;
    println!("   2B model: hidden_size={}, num_layers={}", 
             config_2b.hidden_size, config_2b.num_hidden_layers);

    // Method 3: Manual creation from JSON-like data
    println!("\n3. Manual creation with custom parameters:");
    let custom_config = LlamaConfig {
        hidden_size: 1024,
        intermediate_size: 2816,
        vocab_size: 32000,
        num_hidden_layers: 12,
        num_attention_heads: 16,
        num_key_value_heads: 16,
        use_flash_attn: false,
        rms_norm_eps: 1e-5,
        rope_theta: 10000.0,
        bos_token_id: Some(1),
        eos_token_id: Some(LlamaEosToks::Single(2)),
        rope_scaling: None,
        max_position_embeddings: 2048,
        tie_word_embeddings: false,
    };
    println!("   Custom model: hidden_size={}, num_layers={}", 
             custom_config.hidden_size, custom_config.num_hidden_layers);

    // Method 4: Parsing from JSON string (demonstrating the approach in your code)
    println!("\n4. Parsing from JSON configuration:");
    let json_str = r#"
    {
        "hidden_size": 4096,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "rms_norm_eps": 1e-05,
        "rope_theta": 10000.0,
        "bos_token_id": 1,
        "eos_token_id": 2,
        "max_position_embeddings": 4096,
        "tie_word_embeddings": false
    }"#;
    
    let json_value: serde_json::Value = serde_json::from_str(json_str)?;
    let json_config = create_config_from_json(&json_value)?;
    println!("   From JSON: hidden_size={}, num_layers={}", 
             json_config.hidden_size, json_config.num_hidden_layers);

    println!("\n=== All methods completed successfully! ===");
    Ok(())
}

/// Simplified version of the JSON parsing function for demonstration
fn create_config_from_json(json: &serde_json::Value) -> Result<LlamaConfig> {
    let extract_usize = |field: &str| -> Result<usize> {
        json.get(field)
            .and_then(|v| v.as_u64())
            .map(|v| v as usize)
            .ok_or_else(|| anyhow::anyhow!("Missing field: {}", field))
    };

    let config = LlamaConfig {
        hidden_size: extract_usize("hidden_size")?,
        intermediate_size: extract_usize("intermediate_size")?,
        vocab_size: extract_usize("vocab_size")?,
        num_hidden_layers: extract_usize("num_hidden_layers")?,
        num_attention_heads: extract_usize("num_attention_heads")?,
        num_key_value_heads: extract_usize("num_key_value_heads")?,
        use_flash_attn: false,
        rms_norm_eps: json.get("rms_norm_eps").and_then(|v| v.as_f64()).unwrap_or(1e-5),
        rope_theta: json.get("rope_theta").and_then(|v| v.as_f64()).unwrap_or(10000.0) as f32,
        bos_token_id: json.get("bos_token_id").and_then(|v| v.as_u64()).map(|v| v as u32),
        eos_token_id: json.get("eos_token_id").and_then(|v| v.as_u64()).map(|v| LlamaEosToks::Single(v as u32)),
        rope_scaling: None,
        max_position_embeddings: extract_usize("max_position_embeddings")?,
        tie_word_embeddings: json.get("tie_word_embeddings").and_then(|v| v.as_bool()).unwrap_or(false),
    };

    Ok(config)
}