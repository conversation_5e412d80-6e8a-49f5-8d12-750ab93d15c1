<?php
ini_set('memory_limit', '1024M');
set_time_limit(0);
date_default_timezone_set('Etc/GMT');
include("./vendor/autoload.php");



$qlurk = new \Qlurk\ApiClient(
  "f1lPIBfV8CLu", 
  "lGNXm7nXCOmUx4Lwm5itDtxC96pdxERH", 
  "HrHPD7XeFPgW", 
  "MQAAlJwUyIlSkivWMfy9d7lw2AyfTxr3"
);

$lastDate = date('Y-n-j\TH:i:s',time());

function template($content){
//heredoc string
$template = <<<EOT
{
  "messages": [
    {
      "content": "你是個才華橫溢的SNS 貼文回應者。回覆或總結都是中文的詩詞。不用回覆任何英文版本。最多別超過8行。細心想清楚再寫。",
      "role": "system"
    },
    {
      "content": "Hello!",
      "role": "user"
    }
  ],
  "model": "openchat-3.5-7b",
  "stream": false,
  "max_tokens": 2048,
  "stop": [
    "<|end_of_turn|>"
  ],
  "frequency_penalty": 0,
  "presence_penalty": 0,
  "temperature": 0.9,
  "top_p": 0.95
}
EOT;

$obj = json_decode($template,true);
$obj['messages'][1]['content'] = $content;
return json_encode($obj);
    
}
function postRequest($url, $data){
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //application/json
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    $response = curl_exec($ch);
    curl_close($ch);
    return $response;
}
function br2nl($string)
{
    return preg_replace('/\<br(\s*)?\/?\>/i', "\n", $string);
}

while(true){
    try{
        echo $lastDate."\n";
        $resp = @$qlurk->call('/APP/Alerts/addAllAsFriends');
        $resp = @$qlurk->call('/APP/Polling/getPlurks',['offset'=>$lastDate]);//2020-6-11T21:55:34
        //print_r($resp);
        foreach($resp['plurks'] as $v){


            if(
                $v['qualifier'] == 'feels' || $v['qualifier'] == 'writes' ||
                //wants/needs
                $v['qualifier'] == 'wants' || $v['qualifier'] == 'needs'
            
            ){

                print_r($v);
                $res = postRequest("http://localhost:1337/v1/chat/completions",
                    template($v['content'])
                );

                $res = json_decode($res,true);

                $msgContent = $res['choices'][0]['message']['content'];

                if($msgContent){
                    $qlurk->call('/APP/Responses/responseAdd',[
                        'plurk_id'=>$v['plurk_id'],
                        'content'=>br2nl($msgContent),
                        'qualifier'=>'writes'
                    ]);
                }
                

                //debugging
                echo $msgContent."\n";
                
            }
            $lastDate=date('Y-n-j\TH:i:s',strtotime($v['posted']));
        }
        

    } catch (Exception $e) {
        echo 'Caught exception: ',  $e->getMessage(), "\n";
    }
    sleep(5);
}
