[package]
name = "cpu-poem"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
regex = "1.0"
dotenv = "0.15"
candle-core = "0.9"
candle-nn = "0.9"
candle-transformers = "0.9"
hf-hub = { version = "0.3", features = ["tokio"] }
tokenizers = "0.20"
safetensors = "0.4"