use anyhow::Result;
use candle_core::{Device, Tensor};
use candle_nn::VarBuilder;
use candle_transformers::models::qwen2::{Config as QwenConfig, ModelForCausalLM as QwenModel};

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokenizers::Tokenizer;

/// Load Qwen configuration from JSON
pub fn create_qwen_config_from_json(json: &serde_json::Value) -> Result<QwenConfig> {
    let config: QwenConfig = serde_json::from_value(json.clone())
        .map_err(|e| anyhow::anyhow!("Failed to deserialize Qwen config: {}", e))?;
    Ok(config)
}

pub struct QwenModelWrapper {
    model: QwenModel,
    tokenizer: Tokenizer,
    device: Device,
    config: QwenConfig,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct GenerationConfig {
    pub max_new_tokens: usize,
    pub temperature: f32,
    pub top_p: f32,
    pub system_prompt: String,
}

impl Default for GenerationConfig {
    fn default() -> Self {
        Self {
            max_new_tokens: 200,
            temperature: 0.8,
            top_p: 0.9,
            system_prompt: "你是個才華橫溢的SNS 貼文回應者。回覆或總結都是中文的詩詞。不用回覆任何英文版本。最多別超過8行。細心想清楚再寫。".to_string(),
        }
    }
}

impl QwenModelWrapper {
    /// Load Qwen1.5-MoE-A2.7B model from HuggingFace Hub with local caching
    pub async fn load(cache_dir: &str) -> Result<Self> {
        println!("Loading Qwen1.5-MoE-A2.7B model from HuggingFace Hub...");
        
        let device = Device::Cpu;
        
        // Create cache directory if it doesn't exist
        std::fs::create_dir_all(cache_dir)?;
        
        // Check if model files are already cached locally
        let cache_path = Path::new(cache_dir).join("models--Qwen--Qwen1.5-MoE-A2.7B");
        let is_cached = cache_path.exists() && 
            cache_path.join("config.json").exists() &&
            cache_path.join("tokenizer.json").exists();
        
        let config_path: PathBuf;
        let tokenizer_path: PathBuf;
        let model_files: Vec<PathBuf>;

        if is_cached {
            println!("Model found in cache, loading from local files...");
            config_path = cache_path.join("config.json");
            tokenizer_path = cache_path.join("tokenizer.json");
            
            // Look for model weight files (Qwen1.5-MoE-A2.7B uses 8 sharded files)
            let potential_model_files = vec![
                cache_path.join("model.safetensors"),
                cache_path.join("model-00001-of-00008.safetensors"),
                cache_path.join("model-00002-of-00008.safetensors"),
                cache_path.join("model-00003-of-00008.safetensors"),
                cache_path.join("model-00004-of-00008.safetensors"),
                cache_path.join("model-00005-of-00008.safetensors"),
                cache_path.join("model-00006-of-00008.safetensors"),
                cache_path.join("model-00007-of-00008.safetensors"),
                cache_path.join("model-00008-of-00008.safetensors"),
            ];
            
            model_files = potential_model_files.into_iter()
                .filter(|p| p.exists())
                .collect();
        } else {
            println!("Model not cached, downloading from HuggingFace Hub...");
            
            // Create cache directories
            std::fs::create_dir_all(&cache_path)?;
            
            // Download files directly with reqwest
            println!("Downloading configuration and tokenizer...");
            let base_url = "https://huggingface.co/Qwen/Qwen1.5-MoE-A2.7B/resolve/main";
            
            // Download config.json
            config_path = cache_path.join("config.json");
            let config_url = format!("{}/config.json", base_url);
            let config_content = reqwest::get(&config_url).await?.text().await?;
            std::fs::write(&config_path, config_content)?;
            
            // Download tokenizer.json
            tokenizer_path = cache_path.join("tokenizer.json");
            let tokenizer_url = format!("{}/tokenizer.json", base_url);
            let tokenizer_content = reqwest::get(&tokenizer_url).await?.bytes().await?;
            std::fs::write(&tokenizer_path, tokenizer_content)?;

            // Download model index file for sharded models
            let index_path = cache_path.join("model.safetensors.index.json");
            let index_url = format!("{}/model.safetensors.index.json", base_url);
            let index_content = reqwest::get(&index_url).await?.text().await?;
            std::fs::write(&index_path, index_content)?;
            
            // Download model weights - this will take some time
            println!("Downloading model weights (this may take several minutes)...");
            
            // Download the 8 sharded model files for Qwen1.5-MoE-A2.7B
            let weight_files = vec![
                "model-00001-of-00008.safetensors",
                "model-00002-of-00008.safetensors",
                "model-00003-of-00008.safetensors",
                "model-00004-of-00008.safetensors",
                "model-00005-of-00008.safetensors",
                "model-00006-of-00008.safetensors",
                "model-00007-of-00008.safetensors",
                "model-00008-of-00008.safetensors",
            ];
            
            let mut downloaded_files = Vec::new();
            for weight_file in weight_files {
                let weight_url = format!("{}/{}", base_url, weight_file);
                let weight_path = cache_path.join(weight_file);
                
                println!("Attempting to download {}...", weight_file);
                match reqwest::get(&weight_url).await {
                    Ok(response) => {
                        if response.status().is_success() {
                            let bytes = response.bytes().await?;
                            std::fs::write(&weight_path, bytes)?;
                            downloaded_files.push(weight_path);
                            println!("✅ Downloaded {}", weight_file);
                        } else {
                            println!("⚠️  {} not found (status: {})", weight_file, response.status());
                        }
                    }
                    Err(e) => {
                        println!("⚠️  Failed to download {}: {}", weight_file, e);
                    }
                }
            }
            
            if downloaded_files.is_empty() {
                println!("⚠️  No model weight files could be downloaded.");
                println!("    This might be because the model uses a different file structure.");
                println!("    Proceeding with config-only test...");
            }
            
            model_files = downloaded_files;
        }
        
        // Load tokenizer
        println!("Initializing tokenizer...");
        let tokenizer = Tokenizer::from_file(tokenizer_path)
            .map_err(|e| anyhow::anyhow!("Failed to load tokenizer: {}", e))?;
        
        // Load model config
        let config_content = std::fs::read_to_string(config_path)?;
        let config_json: serde_json::Value = serde_json::from_str(&config_content)
            .map_err(|e| anyhow::anyhow!("Failed to parse model config JSON: {}", e))?;
        
        let config = create_qwen_config_from_json(&config_json)
            .map_err(|e| anyhow::anyhow!("Failed to create QwenConfig: {}", e))?;
        
        // Handle model weights
        if model_files.is_empty() {
            // For testing without downloading large model files, create a mock model
            println!("⚠️  Creating simplified test configuration (no model weights)...");
            return Err(anyhow::anyhow!("Model testing mode: Config and tokenizer loaded successfully, but model weights are not available. This is expected for the initial test."));
        }
        
        // Load model weights (only if files exist)
        println!("Loading model weights into memory...");
        let mut safetensors = Vec::new();
        for (i, file_path) in model_files.iter().enumerate() {
            if !file_path.exists() {
                return Err(anyhow::anyhow!("Model weight file not found: {}", file_path.display()));
            }
            println!("Loading weight file {} of {}...", i + 1, model_files.len());
            let tensors = candle_core::safetensors::load(file_path, &device)
                .map_err(|e| anyhow::anyhow!("Failed to load safetensors file {}: {}", file_path.display(), e))?;
            safetensors.push(tensors);
        }
        
        // Combine all tensors into a single map
        let mut all_tensors = HashMap::new();
        for tensors in safetensors {
            all_tensors.extend(tensors);
        }
        
        println!("Initializing model with {} tensors...", all_tensors.len());
        let vb = VarBuilder::from_tensors(all_tensors, candle_core::DType::F32, &device);
        
        // Initialize Qwen model
        let model = QwenModel::new(&config, vb)
            .map_err(|e| anyhow::anyhow!("Failed to create Qwen model: {}", e))?;
        
        println!("Qwen1.5-MoE-A2.7B model loaded successfully!");
        
        Ok(QwenModelWrapper {
            model,
            tokenizer,
            device,
            config,
        })
    }
    
    /// Generate text response using the loaded model
    pub async fn generate_text(&mut self, prompt: &str, config: &GenerationConfig) -> Result<String> {
        // Create the full prompt with system prompt
        let full_prompt = format!("{}\n\n用戶: {}\n助手: ", config.system_prompt, prompt);
        
        // Tokenize input
        let tokens = self.tokenizer
            .encode(full_prompt, true)
            .map_err(|e| anyhow::anyhow!("Tokenization error: {}", e))?;
        
        let input_ids = tokens.get_ids();
        let input_tensor = Tensor::new(input_ids, &self.device)?
            .unsqueeze(0)?; // Add batch dimension
        
        // Generate tokens
        let mut generated_tokens = input_ids.to_vec();
        let mut logits = self.model.forward(&input_tensor, 0)?;
        
        for step in 0..config.max_new_tokens {
            // Apply temperature scaling
            let temperature_tensor = Tensor::new(config.temperature, &self.device)?;
            logits = logits.broadcast_div(&temperature_tensor)?;
            
            // Apply top-p sampling
            let probabilities = candle_nn::ops::softmax(&logits, candle_core::D::Minus1)?;
            let next_token = self.sample_top_p(&probabilities, config.top_p)?;
            
            generated_tokens.push(next_token);
            
            // Check for end-of-sequence token
            if next_token == self.tokenizer.token_to_id("</s>").unwrap_or(2) {
                println!("End-of-sequence token found at step {}", step);
                break;
            }
            
            // Prepare next input
            let next_input = Tensor::new(&[next_token], &self.device)?
                .unsqueeze(0)?;
            logits = self.model.forward(&next_input, generated_tokens.len() - 1)?;
        }
        
        // Decode only the generated part (skip the input prompt)
        let generated_only = &generated_tokens[input_ids.len()..];
        let response = self.tokenizer
            .decode(generated_only, true)
            .map_err(|e| anyhow::anyhow!("Decoding error: {}", e))?;
        
        // Clean up the response
        let cleaned_response = response
            .trim()
            .replace("</s>", "")
            .trim()
            .to_string();
        
        Ok(cleaned_response)
    }
    
    /// Generate poetry response (convenience method for the main use case)
    pub async fn generate_poetry_response(&mut self, prompt: &str) -> Result<String> {
        let config = GenerationConfig::default();
        self.generate_text(prompt, &config).await
    }
    
    /// Sample from top-p filtered probability distribution
    fn sample_top_p(&self, probabilities: &Tensor, top_p: f32) -> Result<u32> {
        let probabilities = probabilities.flatten_all()?;
        let probs_vec = probabilities.to_vec1::<f32>()?;
        
        // Create indices and sort by probability (descending)
        let mut indexed_probs: Vec<(usize, f32)> = probs_vec
            .iter()
            .enumerate()
            .map(|(i, &p)| (i, p))
            .collect();
        
        indexed_probs.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        
        // Apply top-p filtering
        let mut cumulative_prob = 0.0;
        let mut top_p_indices = Vec::new();
        
        for (idx, prob) in indexed_probs {
            cumulative_prob += prob;
            top_p_indices.push((idx, prob));
            if cumulative_prob >= top_p {
                break;
            }
        }
        
        // Simple sampling: pick the most likely token from top-p set
        // In a more sophisticated implementation, you'd use weighted random sampling
        let selected_idx = top_p_indices[0].0;
        Ok(selected_idx as u32)
    }
    
    /// Get model information
    pub fn model_info(&self) -> ModelInfo {
        ModelInfo {
            vocab_size: self.tokenizer.get_vocab_size(true),
            device: format!("{:?}", self.device),
        }
    }
}

#[derive(Debug)]
pub struct ModelInfo {
    pub vocab_size: usize,
    pub device: String,
}
