use anyhow::Result;
use cpu_poem::{BitNetModel, GenerationConfig};
use std::env;
use std::time::Instant;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv::dotenv().ok();
    
    println!("=== BitNet Model Test Program ===\n");
    
    // Get cache directory from environment or use default
    let cache_dir = env::var("MODEL_CACHE_DIR")
        .unwrap_or_else(|_| "./model_cache".to_string());
    
    println!("Cache directory: {}", cache_dir);
    println!("Loading BitNet model...\n");
    
    // Measure model loading time
    let start_time = Instant::now();
    
    let model = match BitNetModel::load(&cache_dir).await {
        Ok(model) => {
            let load_time = start_time.elapsed();
            println!("✅ Model loaded successfully in {:.2} seconds\n", load_time.as_secs_f32());
            model
        }
        Err(e) => {
            println!("❌ Failed to load model: {}", e);
            return Err(e);
        }
    };
    
    // Display model information
    let model_info = model.model_info();
    println!("Model Information:");
    println!("  - Vocabulary size: {}", model_info.vocab_size);
    println!("  - Device: {}", model_info.device);
    println!();
    
    // Test cases for Chinese poetry generation
    let test_prompts = vec![
        "今天天氣很好",
        "春天來了",
        "我很開心",
        "夜晚的月亮很美",
        "工作很累",
    ];
    
    println!("=== Testing Text Generation ===\n");
    
    // Test with default configuration
    println!("Testing with default configuration:");
    for (i, prompt) in test_prompts.iter().enumerate() {
        println!("Test {}: Input prompt: \"{}\"", i + 1, prompt);
        
        let gen_start = Instant::now();
        match model.generate_poetry_response(prompt).await {
            Ok(response) => {
                let gen_time = gen_start.elapsed();
                println!("✅ Response (generated in {:.2}s):", gen_time.as_secs_f32());
                println!("   {}", response);
                println!();
            }
            Err(e) => {
                println!("❌ Generation failed: {}", e);
                println!();
            }
        }
    }
    
    // Test with custom configuration
    println!("=== Testing Custom Configuration ===\n");
    let custom_config = GenerationConfig {
        max_new_tokens: 100,
        temperature: 0.7,
        top_p: 0.8,
        system_prompt: "你是一個詩人。用簡潔的中文詩句回應。".to_string(),
    };
    
    let test_prompt = "秋天的落葉";
    println!("Custom config test with prompt: \"{}\"", test_prompt);
    
    let gen_start = Instant::now();
    match model.generate_text(test_prompt, &custom_config).await {
        Ok(response) => {
            let gen_time = gen_start.elapsed();
            println!("✅ Custom response (generated in {:.2}s):", gen_time.as_secs_f32());
            println!("   {}", response);
            println!();
        }
        Err(e) => {
            println!("❌ Custom generation failed: {}", e);
            println!();
        }
    }
    
    // Test caching - load model again to verify caching works
    println!("=== Testing Model Caching ===\n");
    println!("Loading model again to test caching...");
    
    let cache_start = Instant::now();
    match BitNetModel::load(&cache_dir).await {
        Ok(_) => {
            let cache_time = cache_start.elapsed();
            println!("✅ Model loaded from cache in {:.2} seconds", cache_time.as_secs_f32());
            
            if cache_time.as_secs_f32() < 10.0 {
                println!("✅ Caching appears to be working (fast reload)");
            } else {
                println!("⚠️  Caching might not be working optimally (slow reload)");
            }
        }
        Err(e) => {
            println!("❌ Failed to load cached model: {}", e);
        }
    }
    
    println!("\n=== Test Complete ===");
    println!("If all tests passed, the model is ready for use in the main application.");
    
    Ok(())
}