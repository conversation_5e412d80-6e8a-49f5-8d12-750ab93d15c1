use anyhow::Result;
use chrono::{DateTime, Utc};
use cpu_poem::BitNetModel;
use dotenv::dotenv;
use regex::Regex;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use tokio::time::sleep;

#[derive(Debug, Serialize, Deserialize)]
struct Plurk {
    plurk_id: i64,
    qualifier: String,
    content: String,
    posted: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct PlurksResponse {
    plurks: Vec<Plurk>,
}

struct QlurkClient {
    client: Client,
    api_key: String,
    secret: String,
    token: String,
    token_secret: String,
}

impl QlurkClient {
    fn new(api_key: &str, secret: &str, token: &str, token_secret: &str) -> Self {
        Self {
            client: Client::new(),
            api_key: api_key.to_string(),
            secret: secret.to_string(),
            token: token.to_string(),
            token_secret: token_secret.to_string(),
        }
    }

    async fn call(&self, endpoint: &str, params: Option<HashMap<String, String>>) -> Result<serde_json::Value> {
        let url = format!("https://www.plurk.com{}", endpoint);
        
        let mut request = self.client.post(&url);
        
        // Add OAuth headers (simplified - in production you'd need proper OAuth signing)
        request = request.header("Authorization", format!("OAuth oauth_consumer_key={}, oauth_token={}", self.api_key, self.token));
        
        if let Some(params) = params {
            request = request.form(&params);
        }

        let response = request.send().await?;
        let json: serde_json::Value = response.json().await?;
        Ok(json)
    }

    async fn get_plurks(&self, offset: &str) -> Result<PlurksResponse> {
        let mut params = HashMap::new();
        params.insert("offset".to_string(), offset.to_string());
        
        let response = self.call("/APP/Polling/getPlurks", Some(params)).await?;
        let plurks_response: PlurksResponse = serde_json::from_value(response)?;
        Ok(plurks_response)
    }

    async fn add_response(&self, plurk_id: i64, content: &str, qualifier: &str) -> Result<()> {
        let mut params = HashMap::new();
        params.insert("plurk_id".to_string(), plurk_id.to_string());
        params.insert("content".to_string(), content.to_string());
        params.insert("qualifier".to_string(), qualifier.to_string());
        
        self.call("/APP/Responses/responseAdd", Some(params)).await?;
        Ok(())
    }

    async fn add_all_as_friends(&self) -> Result<()> {
        self.call("/APP/Alerts/addAllAsFriends", None).await?;
        Ok(())
    }
}

fn br2nl(input: &str) -> String {
    let re = Regex::new(r"<br\s*/??>").unwrap();
    re.replace_all(input, "\n").to_string()
}

fn format_datetime_for_plurk(dt: &DateTime<Utc>) -> String {
    dt.format("%Y-%-m-%-dT%H:%M:%S").to_string()
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    
    println!("=== CPU Poem - BitNet Plurk Bot ===");
    
    let api_key = env::var("QLURK_API_KEY").expect("QLURK_API_KEY must be set in .env file");
    let secret = env::var("QLURK_SECRET").expect("QLURK_SECRET must be set in .env file");
    let token = env::var("QLURK_TOKEN").expect("QLURK_TOKEN must be set in .env file");
    let token_secret = env::var("QLURK_TOKEN_SECRET").expect("QLURK_TOKEN_SECRET must be set in .env file");
    
    let cache_dir = env::var("MODEL_CACHE_DIR").unwrap_or_else(|_| "./model_cache".to_string());
    
    let qlurk = QlurkClient::new(&api_key, &secret, &token, &token_secret);
    
    // Load BitNet model
    println!("Initializing BitNet model...");
    let model = BitNetModel::load(&cache_dir).await?;
    
    let model = Arc::new(Mutex::new(model));
    println!("Model loaded and ready!");
    
    let mut last_date = format_datetime_for_plurk(&Utc::now());

    loop {
        match run_cycle(&qlurk, &model, &mut last_date).await {
            Ok(_) => {},
            Err(e) => {
                eprintln!("Caught exception: {}", e);
            }
        }
        sleep(Duration::from_secs(5)).await;
    }
}

async fn run_cycle(
    qlurk: &QlurkClient,
    model: &Arc<Mutex<BitNetModel>>,
    last_date: &mut String,
) -> Result<()> {
    println!("{}", last_date);

    // Add all as friends
    let _ = qlurk.add_all_as_friends().await;

    // Get plurks
    let resp = qlurk.get_plurks(last_date).await?;

    for plurk in resp.plurks {
        if matches!(
            plurk.qualifier.as_str(),
            "feels" | "writes" | "wants" | "needs"
        ) {
            println!("{:?}", plurk);

            // Generate response using local BitNet model
            let model_guard = model.lock().await;
            let response = model_guard.generate_poetry_response(&plurk.content).await;
            drop(model_guard);

            if let Ok(msg_content) = response {
                if !msg_content.is_empty() {
                    let _ = qlurk
                        .add_response(plurk.plurk_id, &br2nl(&msg_content), "writes")
                        .await;

                    println!("Generated response: {}", msg_content);
                }
            } else {
                eprintln!("Failed to generate response: {:?}", response);
            }
        }

        // Parse the posted datetime and update last_date
        if let Ok(posted_dt) = chrono::DateTime::parse_from_rfc3339(&format!("{}Z", plurk.posted)) {
            *last_date = format_datetime_for_plurk(&posted_dt.with_timezone(&Utc));
        }
    }

    Ok(())
}